"""
Format conversion utilities for 6DOF-GraspNet integration.

This module provides utilities for converting between graspLDM and 6DOF-GraspNet 
data formats, including coordinate system transformations.
"""

from typing import Tuple
import torch

from grasp_ldm.utils.rotations import tmrp_to_H, rotmat_to_quat

from ..config.error_handler import SixDOFIntegrationError, handle_inference_error


class GraspFormatConverter:
    """
    Utility class for converting between graspLDM and 6DOF-Evaluator data formats.

    Handles conversions between:
    - TMRP (Translation + Modified Rodrigues Parameters) ↔ Homogeneous matrices
    - graspLDM coordinate system ↔ 6DOF-Evaluator coordinate system
    - Simplified point cloud centering operations

    Simplified Coordinate System Notes:
    - GraspLDM operates in the original world coordinate system after unnormalization
    - 6DOF-Evaluator expects both point clouds and grasps to be centered at origin
    - This class provides streamlined centering operations that apply consistent
      transformations to both point clouds and grasps using the same centering mean
    - Eliminates redundant denormalization steps for improved efficiency
    """

    @staticmethod
    @handle_inference_error
    def tmrp_to_6dof_format(tmrp_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert TMRP format grasps to 6DOF-Evaluator quaternion+translation format.

        Args:
            tmrp_grasps: [N, 6] TMRP format (translation + MRP)

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format (qw, qx, qy, qz, tx, ty, tz)
        """
        try:
            # Convert TMRP to homogeneous matrices
            H_matrices = tmrp_to_H(tmrp_grasps)  # [N, 4, 4]

            # Extract rotation matrices and translations
            R = H_matrices[..., :3, :3]  # [N, 3, 3]
            t = H_matrices[..., :3, 3]   # [N, 3]

            # Convert rotation matrices to quaternions (wxyz format)
            quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

            # Concatenate quaternions and translations
            qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

            return qt_grasps
            
        except Exception as e:
            raise SixDOFIntegrationError(
                message=f"Failed to convert TMRP to 6DOF format: {str(e)}",
                component="GraspFormatConverter",
                details=e
            ) from e

    @staticmethod
    @handle_inference_error
    def homogeneous_to_6dof_format(H_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert homogeneous transformation matrices to 6DOF-Evaluator format.

        Args:
            H_grasps: [N, 4, 4] homogeneous transformation matrices

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format
        """
        try:
            # Extract rotation matrices and translations
            R = H_grasps[..., :3, :3]  # [N, 3, 3]
            t = H_grasps[..., :3, 3]   # [N, 3]

            # Convert rotation matrices to quaternions (wxyz format)
            quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

            # Concatenate quaternions and translations
            qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

            return qt_grasps
            
        except Exception as e:
            raise SixDOFIntegrationError(
                message=f"Failed to convert homogeneous to 6DOF format: {str(e)}",
                component="GraspFormatConverter",
                details=e
            ) from e

    @staticmethod
    @handle_inference_error
    def prepare_pc_for_6dof(pc: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare point cloud for 6DOF-Evaluator processing with simplified centering operation.

        Args:
            pc: [N, 3] point cloud (already denormalized from graspLDM results)

        Returns:
            tuple: (pc_centered, pc_mean)
                - pc_centered: [N, 3] point cloud centered at origin
                - pc_mean: [3] centroid used for centering (needed for grasp coordinate transforms)
        """
        try:
            # Center point cloud at origin (consistent with GraspEstimator.prepare_pc)
            pc_mean = pc.mean(dim=0)  # [3]
            pc_centered = pc - pc_mean

            return pc_centered, pc_mean
            
        except Exception as e:
            raise SixDOFIntegrationError(
                message=f"Failed to prepare point cloud for 6DOF: {str(e)}",
                component="GraspFormatConverter",
                details=e
            ) from e 