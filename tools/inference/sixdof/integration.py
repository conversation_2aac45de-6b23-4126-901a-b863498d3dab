"""
6DOF-GraspNet integration for grasp evaluation and refinement.

This module provides a complete interface for integrating 6DOF-GraspNet 
capabilities into the GraspLDM inference pipeline.
"""

from typing import Dict, Any, Optional, Tuple
import torch
import numpy as np

from .format_converter import GraspFormatConverter
from ..core.interfaces import SixDOFIntegrator
from ..config.dependency_checker import dependency_manager
from ..config.error_handler import SixDOFIntegrationError, DependencyError, handle_inference_error


class SixDOFIntegratorImpl(SixDOFIntegrator):
    """Implementation of 6DOF-GraspNet integration."""
    
    def __init__(
        self,
        evaluator_model_path: Optional[str] = None,
        refinement_method: str = "gradient",
        refinement_steps: int = 5,
        refinement_threshold: float = 0.7,
        choose_fn: str = "better_than_threshold"
    ):
        """
        Initialize 6DOF integrator.
        
        Args:
            evaluator_model_path: Path to 6DOF evaluator model
            refinement_method: Refinement method ('gradient' or 'sampling')
            refinement_steps: Number of refinement iterations
            refinement_threshold: Success probability threshold
            choose_fn: Grasp selection strategy
        """
        self.evaluator_model_path = evaluator_model_path
        self.refinement_method = refinement_method
        self.refinement_steps = refinement_steps
        self.refinement_threshold = refinement_threshold
        self.choose_fn = choose_fn
        
        # Initialize components
        self.format_converter = GraspFormatConverter()
        self.grasp_evaluator = None
        self.choose_fns = {}
        
        # Setup 6DOF components if available
        self._setup_6dof_components()
    
    def _setup_6dof_components(self):
        """Initialize 6DOF-Evaluator evaluator and related components."""
        if not dependency_manager.is_sixdof_available():
            print("⚠️  6DOF-GraspNet not available, 6DOF integration disabled")
            return
        
        try:
            # Get 6DOF modules
            modules = dependency_manager.get_sixdof_modules()
            create_6dof_model = modules['create_6dof_model']
            grasp_utils = modules['grasp_utils']
            
            # Setup choose functions
            self.choose_fns = {
                "all": None,
                "better_than_threshold": getattr(grasp_utils, 'choose_grasps_better_than_threshold', None),
                "better_than_threshold_in_sequence": getattr(grasp_utils, 'choose_grasps_better_than_threshold_in_sequence', None),
            }
            
            # Create options object for the evaluator
            class EvaluatorOptions:
                def __init__(self, evaluator_model_path):
                    self.arch = "evaluator"
                    self.model_scale = 1
                    self.pointnet_radius = 0.02
                    self.pointnet_nclusters = 128
                    self.gpu_ids = [0] if torch.cuda.is_available() else []
                    self.init_type = 'normal'
                    self.init_gain = 0.02
                    self.is_train = False
                    self.continue_train = False

                    # Set checkpoint directory and model name
                    if evaluator_model_path and os.path.exists(evaluator_model_path):
                        import os
                        self.checkpoints_dir = os.path.dirname(evaluator_model_path)
                        filename = os.path.basename(evaluator_model_path)
                        self.which_epoch = filename.split('_net.pth')[0] if '_net.pth' in filename else 'latest'
                        self.name = os.path.basename(self.checkpoints_dir)
                    else:
                        import os
                        self.checkpoints_dir = os.path.join(os.path.dirname(__file__), '../../../sixdof_evaluator/checkpoints')
                        self.name = 'evaluator_pretrained'
                        self.which_epoch = 'latest'

            evaluator_opt = EvaluatorOptions(self.evaluator_model_path)
            self.grasp_evaluator = create_6dof_model(evaluator_opt)
            
            print(f"✓ 6DOF-Evaluator integrator initialized")
            
        except Exception as e:
            print(f"❌ Failed to initialize 6DOF components: {e}")
            self.grasp_evaluator = None
    
    def is_available(self) -> bool:
        """Check if 6DOF integration is available."""
        return dependency_manager.is_sixdof_available() and self.grasp_evaluator is not None
    
    @handle_inference_error
    def evaluate_grasps(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor
    ) -> torch.Tensor:
        """
        Evaluate grasps using 6DOF-Evaluator evaluator.

        Args:
            pointcloud: [num_points, 3] denormalized point cloud
            grasps: [num_grasps, 4, 4] homogeneous transformation matrices

        Returns:
            torch.Tensor: [num_grasps] success probabilities
        """
        if not self.is_available():
            print("⚠️  6DOF evaluation not available, returning dummy scores")
            return torch.ones(grasps.shape[0], device=grasps.device) * 0.5

        try:
            # Get 6DOF modules
            modules = dependency_manager.get_sixdof_modules()
            grasp_utils = modules['grasp_utils']
            
            # Step 1: Center point cloud and get centering mean
            pc_for_6dof, pc_mean = self.format_converter.prepare_pc_for_6dof(pointcloud)

            # Step 2: Transform grasps to centered coordinate system
            grasps_centered = grasps.clone()
            grasps_centered[..., :3, 3] -= pc_mean

            # Step 3: Convert centered grasps to 6DOF format
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps_centered)

            # Step 4: Generate gripper control points
            with torch.no_grad():
                grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)
                grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_translations, device=grasps.device
                )

                # Step 5: Prepare batched point cloud
                num_grasps = grasp_pcs.shape[0]
                pc_batch = pc_for_6dof.unsqueeze(0).repeat(num_grasps, 1, 1)  # [G, N, 3]

                # Step 6: Evaluate grasps
                success_probs = self.grasp_evaluator.evaluate_grasps(pc_batch, grasp_pcs)

                return success_probs.squeeze(-1)

        except Exception as e:
            raise SixDOFIntegrationError(
                message=f"Failed to evaluate grasps with 6DOF: {str(e)}",
                component="SixDOFIntegratorImpl",
                details=e
            ) from e

    @handle_inference_error
    def refine_grasps(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor,
        method: Optional[str] = None,
        num_steps: Optional[int] = None,
        choose_fn: Optional[str] = None,
        track_refinement_process: bool = False,
        **kwargs
    ) -> Tuple[torch.Tensor, torch.Tensor, Optional[Dict[str, Any]]]:
        """
        Refine grasps using 6DOF-Evaluator refinement methods.

        Args:
            pointcloud: [N, 3] denormalized point cloud
            grasps: [G, 4, 4] initial grasp poses
            method: Refinement method (overrides instance setting)
            num_steps: Number of refinement iterations (overrides instance setting)
            choose_fn: Grasp selection strategy (overrides instance setting)
            track_refinement_process: Whether to track refinement process

        Returns:
            tuple: (refined_grasps, success_probabilities, refinement_data)
        """
        if not self.is_available():
            print("⚠️  6DOF refinement not available, returning original grasps")
            success_probs = self.evaluate_grasps(pointcloud, grasps)
            return grasps, success_probs, None

        # Use provided parameters or fall back to instance defaults
        refine_method = method if method is not None else self.refinement_method
        refine_steps = num_steps if num_steps is not None else self.refinement_steps
        active_choose_fn = choose_fn if choose_fn is not None else self.choose_fn

        try:
            # Get 6DOF modules
            modules = dependency_manager.get_sixdof_modules()
            grasp_utils = modules['grasp_utils']
            
            # Prepare coordinate system transformation
            pc_for_6dof, pc_mean = self.format_converter.prepare_pc_for_6dof(pointcloud)
            
            num_grasps = grasps.shape[0]
            pc_batch = pc_for_6dof.unsqueeze(0).repeat(num_grasps, 1, 1)

            # Transform grasps to centered coordinates
            grasps_centered = grasps.clone()
            grasps_centered[..., :3, 3] -= pc_mean
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps_centered)

            # Convert to euler angles and translations
            grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)

            # Setup refinement method
            if refine_method == "gradient":
                grasp_eulers = torch.autograd.Variable(
                    grasp_eulers.to(grasps.device), requires_grad=True
                )
                grasp_translations = torch.autograd.Variable(
                    grasp_translations.to(grasps.device), requires_grad=True
                )
                improve_fun = self._improve_grasps_gradient_based
            else:
                improve_fun = self._improve_grasps_sampling_based

            # Initialize tracking
            if track_refinement_process:
                improved_success = []
                improved_eulers = []
                improved_ts = []
                
                # Store initial poses
                improved_eulers.append(grasp_eulers.cpu().data.numpy())
                improved_ts.append(grasp_translations.cpu().data.numpy())

            last_success = None

            # Refinement loop
            for i in range(refine_steps):
                success_prob, last_success = improve_fun(
                    pc_batch, grasp_eulers, grasp_translations, last_success
                )
                
                if track_refinement_process:
                    improved_success.append(success_prob.cpu().data.numpy())
                    improved_eulers.append(grasp_eulers.cpu().data.numpy())
                    improved_ts.append(grasp_translations.cpu().data.numpy())

            # Final evaluation
            grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                grasp_eulers, grasp_translations, grasps.device
            )
            final_evaluation = self.grasp_evaluator.evaluate_grasps(pc_batch, grasp_pcs)
            
            if track_refinement_process:
                improved_success.append(final_evaluation.squeeze(-1).cpu().data.numpy())

            # Apply selection strategy
            if track_refinement_process:
                improved_eulers_np = np.asarray(improved_eulers)
                improved_ts_np = np.asarray(improved_ts)
                improved_success_np = np.asarray(improved_success)
            else:
                # For non-tracking mode, use simplified selection
                improved_eulers_np = grasp_eulers.cpu().data.numpy()[np.newaxis]
                improved_ts_np = grasp_translations.cpu().data.numpy()[np.newaxis]
                improved_success_np = final_evaluation.squeeze(-1).cpu().data.numpy()[np.newaxis]

            # Apply grasp selection
            if active_choose_fn == "all" or self.choose_fns[active_choose_fn] is None:
                selection_mask = np.ones(improved_eulers_np.shape[:2], dtype=np.float32)
            else:
                selection_mask = self.choose_fns[active_choose_fn](
                    improved_eulers_np, improved_ts_np, improved_success_np, self.refinement_threshold
                )

            # Convert selected grasps back to homogeneous matrices
            grasps_list = grasp_utils.rot_and_trans_to_grasps(
                improved_eulers_np, improved_ts_np, selection_mask
            )
            
            refined_grasps_centered = torch.from_numpy(
                np.stack(grasps_list)
            ).to(grasps.device).float()

            # Extract success scores for selected grasps
            refine_indexes, sample_indexes = np.where(selection_mask)
            selected_success_np = improved_success_np[refine_indexes, sample_indexes]
            refine_scores = torch.from_numpy(selected_success_np).to(grasps.device).float()

            # Transform back to original coordinates
            refined_grasps = refined_grasps_centered.clone()
            refined_grasps[..., :3, 3] += pc_mean

            # Prepare refinement data if tracking
            refinement_data = None
            if track_refinement_process:
                refinement_data = {
                    'improved_eulers': improved_eulers_np,
                    'improved_ts': improved_ts_np,
                    'improved_success': improved_success_np,
                    'selection_mask': selection_mask,
                    'pc_mean': pc_mean.cpu().numpy(),
                    'pointcloud_centered': pc_for_6dof.cpu().numpy(),
                    'pointcloud_original': pointcloud.cpu().numpy(),
                    'method': refine_method,
                    'num_steps': refine_steps,
                    'selection_strategy': active_choose_fn,
                    'threshold': self.refinement_threshold,
                    'selected_step_indices': refine_indexes,
                    'selected_grasp_indices': sample_indexes,
                    'num_grasps': num_grasps,
                    'num_refinement_steps': refine_steps + 1,
                    'num_selected_grasps': len(refine_indexes),
                }

            return refined_grasps, refine_scores, refinement_data

        except Exception as e:
            raise SixDOFIntegrationError(
                message=f"Failed to refine grasps with 6DOF: {str(e)}",
                component="SixDOFIntegratorImpl",
                details=e
            ) from e

    def _improve_grasps_gradient_based(self, pcs, grasp_eulers, grasp_trans, last_success):
        """Gradient-based grasp improvement."""
        modules = dependency_manager.get_sixdof_modules()
        grasp_utils = modules['grasp_utils']
        
        # Clear gradients
        if grasp_eulers.grad is not None:
            grasp_eulers.grad.zero_()
        if grasp_trans.grad is not None:
            grasp_trans.grad.zero_()

        # Generate control points and evaluate
        grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
            grasp_eulers, grasp_trans, grasp_eulers.device
        )
        pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)
        success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)

        # Backpropagate
        success.squeeze(-1).backward(torch.ones(success.shape[0]).to(grasp_eulers.device))

        # Update with adaptive step size
        delta_t = grasp_trans.grad
        norm_t = torch.norm(delta_t, p=2, dim=-1).to(grasp_eulers.device)
        alpha = torch.min(0.01 / norm_t, torch.tensor(1.0).to(grasp_eulers.device))
        
        grasp_trans.data += grasp_trans.grad * alpha[:, None]
        grasp_eulers.data += grasp_eulers.grad * alpha[:, None]

        return success.squeeze(-1), None

    def _improve_grasps_sampling_based(self, pcs, grasp_eulers, grasp_trans, last_success):
        """Sampling-based grasp improvement."""
        modules = dependency_manager.get_sixdof_modules()
        grasp_utils = modules['grasp_utils']
        
        with torch.no_grad():
            # Evaluate current poses if not provided
            if last_success is None:
                grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_trans, grasp_eulers.device
                )
                pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)
                last_success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)

            # Generate perturbations
            delta_t = 2 * (torch.rand(grasp_trans.shape).to(grasp_eulers.device) - 0.5) * 0.02
            delta_euler_angles = (torch.rand(grasp_eulers.shape).to(grasp_eulers.device) - 0.5) * 2

            # Apply perturbations
            perturbed_translation = grasp_trans + delta_t
            perturbed_euler_angles = grasp_eulers + delta_euler_angles

            # Evaluate perturbed poses
            grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                perturbed_euler_angles, perturbed_translation, grasp_eulers.device
            )
            pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)
            perturbed_success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)

            # Acceptance criterion
            ratio = perturbed_success / torch.max(
                last_success, torch.tensor(0.0001).to(grasp_eulers.device)
            )
            mask = torch.rand(ratio.shape).to(grasp_eulers.device) <= ratio

            # Update accepted samples
            next_success = last_success
            ind = torch.where(mask)[0]
            next_success[ind] = perturbed_success[ind]
            grasp_trans[ind].data = perturbed_translation.data[ind]
            grasp_eulers[ind].data = perturbed_euler_angles.data[ind]

            return last_success.squeeze(-1), next_success 