"""
Abstract interfaces for modular inference system.

This module defines the core abstract interfaces that components must implement
to ensure consistent behavior and enable flexible component substitution.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, Tuple
import torch
import numpy as np


class InferenceStrategy(ABC):
    """Abstract base class for inference strategies."""
    
    @abstractmethod
    def generate_grasps(
        self,
        pointcloud: torch.Tensor,
        num_grasps: int,
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """Generate grasps from point cloud."""
        pass


class VisualizationStrategy(ABC):
    """Abstract base class for visualization strategies."""
    
    @abstractmethod
    def visualize(
        self,
        results: Dict[str, torch.Tensor],
        save_path: Optional[str] = None,
        show_scene: bool = True,
        return_scene: bool = False,
        **kwargs
    ) -> Optional[Any]:
        """Visualize inference results."""
        pass


class SixDOFIntegrator(ABC):
    """Abstract base class for 6DOF integration strategies."""
    
    @abstractmethod
    def evaluate_grasps(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor
    ) -> torch.Tensor:
        """Evaluate grasps using 6DOF evaluator."""
        pass
    
    @abstractmethod
    def refine_grasps(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor,
        **kwargs
    ) -> Tuple[torch.Tensor, torch.Tensor, Optional[Dict[str, Any]]]:
        """Refine grasps using 6DOF refinement methods."""
        pass


class DataProcessor(ABC):
    """Abstract base class for data processing strategies."""
    
    @abstractmethod
    def prepare_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        **kwargs
    ) -> torch.Tensor:
        """Prepare point cloud for inference."""
        pass
    
    @abstractmethod
    def preprocess_pointcloud(
        self,
        pointcloud: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """Preprocess point cloud with normalization."""
        pass 