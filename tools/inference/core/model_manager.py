"""
Model management for GraspLDM inference.

This module handles loading and configuring the GraspLDM model, including
VAE integration, normalization parameters, and diffusion sampler setup.
"""

import os
import warnings
from typing import Optional

import torch
import torch.nn as nn

from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from tools.inference import Experiment, fix_state_dict_prefix

from ..config.error_handler import ModelLoadError, handle_inference_error


class ModelManager:
    """Manages GraspLDM model loading and configuration."""
    
    def __init__(self, exp_path: str, device: str = "cuda:0", use_ema_model: bool = False):
        """
        Initialize model manager.
        
        Args:
            exp_path: Path to experiment directory containing model checkpoints
            device: PyTorch device for computation
            use_ema_model: Whether to use Exponential Moving Average weights
        """
        self.exp_path = exp_path
        self.device = torch.device(device)
        self.use_ema_model = use_ema_model
        
        # Initialize experiment handler
        self.experiment = Experiment(
            exp_name=os.path.basename(exp_path),
            exp_out_root=os.path.dirname(exp_path),
            modes=["ddm", "vae"]
        )
        
        # Load configuration
        self.config = self.experiment.get_config("ddm")
        
        # Initialize model and normalization parameters
        self.model = None
        self.num_points = self.config.pc_num_points
        self._sigmoid = nn.Sigmoid()
        
        # Normalization parameters (will be set by _set_normalization_params)
        self._INPUT_PC_SHIFT = None
        self._INPUT_PC_SCALE = None
        self._INPUT_GRASP_SHIFT = None
        self._INPUT_GRASP_SCALE = None
    
    @handle_inference_error
    def setup_ldm_sampler(self, num_inference_steps: Optional[int], use_fast_sampler: bool):
        """
        Configure the diffusion sampler parameters.
        
        Args:
            num_inference_steps: Number of denoising steps for diffusion
            use_fast_sampler: Enable fast sampling with DDIM scheduler
        """
        if use_fast_sampler:
            self.config.models.ddm.model.args.noise_scheduler_type = "ddim"
            self.fast_sampler = "DDIM"
            self.num_inference_steps = 100 if num_inference_steps is None else num_inference_steps
        else:
            self.fast_sampler = None
            self.num_inference_steps = 1000 if num_inference_steps is None else num_inference_steps
    
    @handle_inference_error
    def load_model(self):
        """Load the trained LDM model with VAE."""
        try:
            # Build model from configuration
            model = build_model_from_cfg(self.config.model.ddm)
            model.set_vae_model(build_model_from_cfg(self.config.model.vae))

            # Load checkpoint
            ckpt_path = self.experiment.get_ckpt_path("ddm")
            if not os.path.exists(ckpt_path):
                raise ModelLoadError(
                    message=f"Checkpoint not found: {ckpt_path}",
                    component="ModelManager"
                )
            
            state_dict = torch.load(ckpt_path, map_location=self.device)["state_dict"]
            
            # Use appropriate model prefix (EMA vs standard)
            model_prefix = "model" if not self.use_ema_model else "ema_model.online_model"
            state_dict = fix_state_dict_prefix(state_dict, model_prefix, ignore_all_others=True)

            # Load weights
            missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=True)
            if missing_keys:
                warnings.warn(f"Missing keys while loading state dict: {missing_keys}")
            if unexpected_keys:
                warnings.warn(f"Found unexpected keys while loading state dict: {unexpected_keys}")

            self.model = model.eval().to(self.device)
            return self.model
            
        except Exception as e:
            raise ModelLoadError(
                message=f"Failed to load model: {str(e)}",
                component="ModelManager",
                details=e
            ) from e
    
    @handle_inference_error
    def set_normalization_params(self):
        """
        Set normalization parameters based on dataset statistics.

        These parameters are derived from the training data and must match
        exactly for proper model performance. The values are based on the
        AcronymPartialPointclouds dataset preprocessing pipeline.
        """
        # Point cloud normalization (zero-centered after per-object centering)
        self._INPUT_PC_SHIFT = torch.zeros((3,), device=self.device)
        self._INPUT_PC_SCALE = torch.ones((3,), device=self.device) * 0.05  # translation_scale

        # Grasp normalization (zero-centered after per-object centering)
        self._INPUT_GRASP_SHIFT = torch.zeros((6,), device=self.device)
        self._INPUT_GRASP_SCALE = torch.cat([
            torch.ones((3,), device=self.device) * 0.05,  # translation_scale
            torch.ones((3,), device=self.device) * 0.5,   # rotation_scale
        ])

        print(f"✓ Normalization parameters set:")
        print(f"  PC scale: {self._INPUT_PC_SCALE[0].item():.3f}")
        print(f"  Grasp translation scale: {self._INPUT_GRASP_SCALE[0].item():.3f}")
        print(f"  Grasp rotation scale: {self._INPUT_GRASP_SCALE[3].item():.3f}")
    
    def get_normalization_params(self):
        """Get normalization parameters."""
        return {
            'pc_shift': self._INPUT_PC_SHIFT,
            'pc_scale': self._INPUT_PC_SCALE,
            'grasp_shift': self._INPUT_GRASP_SHIFT,
            'grasp_scale': self._INPUT_GRASP_SCALE
        }
    
    def initialize(self, num_inference_steps: Optional[int] = 100, use_fast_sampler: bool = True):
        """
        Complete initialization of the model manager.
        
        Args:
            num_inference_steps: Number of denoising steps for diffusion
            use_fast_sampler: Enable fast sampling with DDIM scheduler
        """
        self.setup_ldm_sampler(num_inference_steps, use_fast_sampler)
        self.load_model()
        self.set_normalization_params()
        
        print(f"✓ Model Manager initialized:")
        print(f"  Device: {self.device}")
        print(f"  Model: {'EMA' if self.use_ema_model else 'Standard'}")
        print(f"  Sampler: {'Fast DDIM' if use_fast_sampler else 'Standard DDPM'}")
        print(f"  Inference steps: {self.num_inference_steps}")
        print(f"  Point cloud size: {self.num_points}")
        
        return self 