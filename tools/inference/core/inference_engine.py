"""
Core inference engine for GraspLDM.

This module contains the main inference logic for generating grasps using
the latent diffusion model.
"""

from typing import Dict, List, Tuple
import torch
import torch.nn as nn

from grasp_ldm.utils.rotations import tmrp_to_H
from tools.inference import unnormalize_grasps, unnormalize_pc

from .interfaces import InferenceStrategy
from ..config.error_handler import InferenceError, handle_inference_error


class GraspInferenceEngine(InferenceStrategy):
    """Core inference engine for generating grasps using GraspLDM."""
    
    def __init__(self, model_manager):
        """
        Initialize inference engine.
        
        Args:
            model_manager: ModelManager instance with loaded model
        """
        self.model_manager = model_manager
        self.model = model_manager.model
        self.device = model_manager.device
        self.fast_sampler = model_manager.fast_sampler
        self.num_inference_steps = model_manager.num_inference_steps
        self._sigmoid = nn.Sigmoid()
        
    @handle_inference_error
    def generate_grasps(
        self,
        pointcloud: torch.Tensor,
        metas: Dict[str, torch.Tensor],
        num_grasps: int = 20,
        return_intermediate: bool = False,
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """
        Generate grasps using the LDM model.

        This method implements the core LDM inference pipeline:
        1. Point cloud encoding to latent space
        2. Diffusion sampling in grasp latent space
        3. VAE decoding to grasp poses
        4. Post-processing and unnormalization

        Args:
            pointcloud: Preprocessed point cloud [num_points, 3]
            metas: Metadata dictionary for unnormalization
            num_grasps: Number of grasps to generate
            return_intermediate: Whether to return intermediate diffusion steps

        Returns:
            dict: Generated results containing:
                - grasps: [num_grasps, 4, 4] homogeneous transformation matrices
                - confidence: [num_grasps, 1] success probabilities
                - pc: [num_points, 3] unnormalized point cloud
                - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
        """
        try:
            # Ensure batch dimension
            batch_pc = pointcloud.unsqueeze(0)  # [1, num_points, 3]

            # Move metadata to device
            metas = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                    for k, v in metas.items()}

            # Prepare model input
            in_kwargs = {"xyz": batch_pc, "metas": metas}

            # Configure fast sampling if enabled
            if self.fast_sampler == "DDIM":
                self.model.set_inference_timesteps(self.num_inference_steps)

            # Execute LDM inference with intermediate step control
            with torch.no_grad():
                final_grasps, all_diffusion_grasps = self.model.generate_grasps(
                    num_grasps=num_grasps, 
                    return_intermediate=return_intermediate, 
                    **in_kwargs
                )

            # Parse model outputs
            if self.model.vae_model.decoder._use_qualities:
                tmrp, cls_logit, qualities = final_grasps
            else:
                tmrp, cls_logit = final_grasps
                qualities = None

            # Reshape to proper dimensions
            tmrp = tmrp.view(1, num_grasps, tmrp.shape[-1])

            # Unnormalize grasp poses
            grasp_unnorm = unnormalize_grasps(tmrp, metas)

            # Convert to homogeneous transformation matrices
            H_grasps = tmrp_to_H(grasp_unnorm)  # [1, num_grasps, 4, 4]

            # Process intermediate diffusion steps if available
            all_steps_grasps = []
            if return_intermediate and all_diffusion_grasps:
                # Note: For batched inference (batch_size > 1), intermediate steps are not supported
                # This follows the same limitation as InferenceLDM
                if batch_pc.shape[0] > 1:
                    print("⚠️  Warning: Batched inference with intermediate steps not supported. Skipping intermediate results.")
                else:
                    for step_grasp in all_diffusion_grasps:
                        # Each step_grasp is (tmrp, cls_logit) or (tmrp, cls_logit, qualities)
                        step_tmrp = step_grasp[0]
                        # Reshape to ensure num_grasps dimension is consistently present
                        reshaped_step_tmrp = step_tmrp.view(1, num_grasps, -1)
                        step_grasp_unnorm = unnormalize_grasps(reshaped_step_tmrp, metas)
                        step_H_grasps = tmrp_to_H(step_grasp_unnorm)
                        all_steps_grasps.append(step_H_grasps.squeeze(0))  # Remove batch dimension

            # Compute confidence scores (This value is wrong, need to be fixed)
            confidence = cls_logit.view(1, num_grasps, cls_logit.shape[-1])
            confidence = self._sigmoid(confidence)
            
            # Unnormalize point cloud
            pc_unnorm = unnormalize_pc(batch_pc, metas)

            return {
                "grasps": H_grasps.squeeze(0),      # [num_grasps, 4, 4]
                "grasp_tmrp": grasp_unnorm.squeeze(0),  # [num_grasps, 6] translation + rotation MRP
                "confidence": confidence.squeeze(0), # [num_grasps, 1]
                "pc": pc_unnorm.squeeze(0),         # [num_points, 3]
                "qualities": qualities,              # Optional quality metrics
                "all_steps_grasps": all_steps_grasps, # List of intermediate diffusion steps
            }
            
        except Exception as e:
            raise InferenceError(
                message=f"Failed to generate grasps: {str(e)}",
                component="GraspInferenceEngine",
                details=e
            ) from e 