"""
Dependency management for optional components.

This module handles conditional imports and availability checks for optional
dependencies like 6DOF-GraspNet, providing clean fallback mechanisms.
"""

import os
import sys
from typing import Dict, Any, Optional, Tuple
from .error_handler import DependencyError


class DependencyManager:
    """Manager for handling optional dependencies and their availability."""
    
    def __init__(self):
        self._dependency_cache: Dict[str, Dict[str, Any]] = {}
        self._initialize_dependencies()
    
    def _initialize_dependencies(self):
        """Initialize all dependency checks."""
        self._check_sixdof_availability()
        self._check_visualization_dependencies()
    
    def _check_sixdof_availability(self) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
        """Check if 6DOF-Evaluator is available and importable."""
        try:
            # Get the absolute path to the current file's directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Build absolute path to sixdof_evaluator directory
            sixdof_path = os.path.abspath(os.path.join(current_dir, '..', '..', '..', 'sixdof_evaluator'))
            
            # Verify the path exists
            if not os.path.exists(sixdof_path):
                self._dependency_cache['sixdof'] = {
                    'available': False,
                    'error': f"6DOF-Evaluator directory not found at: {sixdof_path}",
                    'modules': None
                }
                return False, f"Directory not found: {sixdof_path}", None
            
            # Check if models directory exists
            models_path = os.path.join(sixdof_path, 'models')
            if not os.path.exists(models_path):
                self._dependency_cache['sixdof'] = {
                    'available': False,
                    'error': f"6DOF-Evaluator models directory not found at: {models_path}",
                    'modules': None
                }
                return False, f"Models directory not found: {models_path}", None
            
            # Add the 6dof_evaluator directory to path
            if sixdof_path not in sys.path:
                sys.path.insert(0, sixdof_path)
            
            # Try to import 6DOF-GraspNet components
            from models import create_model as create_6dof_model
            from utils import utils as grasp_utils
            from utils.visualization_utils import draw_scene
            
            modules = {
                'create_6dof_model': create_6dof_model,
                'grasp_utils': grasp_utils,
                'draw_scene': draw_scene
            }
            
            self._dependency_cache['sixdof'] = {
                'available': True,
                'error': None,
                'modules': modules,
                'path': sixdof_path
            }
            
            return True, None, modules
            
        except ImportError as e:
            error_msg = f"6DOF-Evaluator import error: {e}"
            self._dependency_cache['sixdof'] = {
                'available': False,
                'error': error_msg,
                'modules': None
            }
            return False, error_msg, None
        except Exception as e:
            error_msg = f"Unexpected error loading 6DOF-Evaluator: {e}"
            self._dependency_cache['sixdof'] = {
                'available': False,
                'error': error_msg,
                'modules': None
            }
            return False, error_msg, None
    
    def _check_visualization_dependencies(self):
        """Check visualization-related dependencies."""
        deps = {
            'trimesh': False,
            'matplotlib': False,
            'PIL': False,
            'imageio': False
        }
        
        try:
            import trimesh
            deps['trimesh'] = True
        except ImportError:
            pass
        
        try:
            import matplotlib.pyplot as plt
            deps['matplotlib'] = True
        except ImportError:
            pass
        
        try:
            from PIL import Image
            deps['PIL'] = True
        except ImportError:
            pass
        
        try:
            import imageio
            deps['imageio'] = True
        except ImportError:
            pass
        
        self._dependency_cache['visualization'] = {
            'available': deps['trimesh'],  # trimesh is core requirement
            'optional': deps,
            'error': None if deps['trimesh'] else "trimesh not available"
        }
    
    def is_sixdof_available(self) -> bool:
        """Check if 6DOF-GraspNet is available."""
        return self._dependency_cache.get('sixdof', {}).get('available', False)
    
    def get_sixdof_modules(self) -> Optional[Dict[str, Any]]:
        """Get 6DOF-GraspNet modules if available."""
        if self.is_sixdof_available():
            return self._dependency_cache['sixdof']['modules']
        return None
    
    def get_sixdof_error(self) -> Optional[str]:
        """Get 6DOF-GraspNet error message if not available."""
        return self._dependency_cache.get('sixdof', {}).get('error')
    
    def is_visualization_available(self) -> bool:
        """Check if basic visualization is available."""
        return self._dependency_cache.get('visualization', {}).get('available', False)
    
    def get_visualization_capabilities(self) -> Dict[str, bool]:
        """Get available visualization capabilities."""
        return self._dependency_cache.get('visualization', {}).get('optional', {})
    
    def require_dependency(self, dependency: str) -> Any:
        """Require a dependency, raise error if not available."""
        if dependency == 'sixdof':
            if not self.is_sixdof_available():
                raise DependencyError(
                    message=f"6DOF-GraspNet is required but not available: {self.get_sixdof_error()}",
                    component="DependencyManager"
                )
            return self.get_sixdof_modules()
        elif dependency == 'visualization':
            if not self.is_visualization_available():
                raise DependencyError(
                    message="Visualization dependencies are required but not available",
                    component="DependencyManager"
                )
            return self._dependency_cache['visualization']
        else:
            raise DependencyError(
                message=f"Unknown dependency: {dependency}",
                component="DependencyManager"
            )
    
    def get_status_report(self) -> str:
        """Get a human-readable status report of all dependencies."""
        report = ["Dependency Status Report:"]
        
        # 6DOF status
        sixdof_status = "✓ Available" if self.is_sixdof_available() else f"✗ Not available: {self.get_sixdof_error()}"
        report.append(f"  6DOF-GraspNet: {sixdof_status}")
        
        # Visualization status
        viz_caps = self.get_visualization_capabilities()
        viz_status = "✓ Available" if self.is_visualization_available() else "✗ Not available"
        report.append(f"  Visualization: {viz_status}")
        
        if viz_caps:
            for lib, available in viz_caps.items():
                status = "✓" if available else "✗"
                report.append(f"    {lib}: {status}")
        
        return "\n".join(report)


# Global dependency manager instance
dependency_manager = DependencyManager() 