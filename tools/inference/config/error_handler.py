"""
Unified error handling for the inference system.

This module provides custom exception classes and error handling utilities
to ensure consistent error reporting across all components.
"""

from typing import Optional, Any


class InferenceError(Exception):
    """Base exception for inference-related errors."""
    
    def __init__(self, message: str, component: Optional[str] = None, details: Optional[Any] = None):
        self.message = message
        self.component = component
        self.details = details
        super().__init__(self.message)
    
    def __str__(self):
        if self.component:
            return f"[{self.component}] {self.message}"
        return self.message


class ModelLoadError(InferenceError):
    """Exception raised when model loading fails."""
    pass


class PreprocessingError(InferenceError):
    """Exception raised during point cloud preprocessing."""
    pass


class SixDOFIntegrationError(InferenceError):
    """Exception raised during 6DOF integration."""
    pass


class VisualizationError(InferenceError):
    """Exception raised during visualization."""
    pass


class ConfigurationError(InferenceError):
    """Exception raised due to invalid configuration."""
    pass


class DependencyError(InferenceError):
    """Exception raised when required dependencies are missing."""
    pass


def handle_inference_error(func):
    """Decorator to provide consistent error handling for inference methods."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except InferenceError:
            # Re-raise inference errors as-is
            raise
        except Exception as e:
            # Wrap other exceptions as InferenceError
            component = getattr(args[0], '__class__', {}).get('__name__', 'Unknown')
            raise InferenceError(
                message=f"Unexpected error in {func.__name__}: {str(e)}",
                component=component,
                details=e
            ) from e
    return wrapper 